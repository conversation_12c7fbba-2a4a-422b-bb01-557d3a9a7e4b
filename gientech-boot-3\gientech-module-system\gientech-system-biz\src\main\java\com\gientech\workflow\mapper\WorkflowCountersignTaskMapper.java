package com.gientech.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gientech.workflow.entity.WorkflowCountersignTask;
import com.gientech.workflow.enums.CountersignTaskStatus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流会签任务 数据库映射
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
public interface WorkflowCountersignTaskMapper extends BaseMapper<WorkflowCountersignTask> {

    /**
     * 根据主任务ID查询所有会签子任务
     *
     * @param workflowTaskId 主任务ID
     * @return 会签子任务列表
     */
    List<WorkflowCountersignTask> selectByWorkflowTaskId(@Param("workflowTaskId") String workflowTaskId);

    /**
     * 根据主任务ID和状态查询会签子任务
     *
     * @param workflowTaskId 主任务ID
     * @param status 任务状态
     * @return 会签子任务列表
     */
    List<WorkflowCountersignTask> selectByWorkflowTaskIdAndStatus(@Param("workflowTaskId") String workflowTaskId,
                                                                   @Param("status") CountersignTaskStatus status);

    /**
     * 根据委托人查询待处理的会签任务
     *
     * @param assignee 委托人
     * @param assigneeOrgCode 委托人机构
     * @return 会签子任务列表
     */
    List<WorkflowCountersignTask> selectPendingTasksByAssignee(@Param("assignee") String assignee,
                                                               @Param("assigneeOrgCode") String assigneeOrgCode);

    /**
     * 统计主任务下各状态的会签子任务数量
     *
     * @param workflowTaskId 主任务ID
     * @return 统计结果Map，key为状态，value为数量
     */
    List<CountersignTaskStatusCount> countByWorkflowTaskIdGroupByStatus(@Param("workflowTaskId") String workflowTaskId);

    /**
     * 批量更新会签任务状态
     *
     * @param taskIds 任务ID列表
     * @param status 新状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("taskIds") List<String> taskIds,
                          @Param("status") CountersignTaskStatus status,
                          @Param("updateBy") String updateBy);

    /**
     * 查询超时的会签任务
     *
     * @return 超时任务列表
     */
    List<WorkflowCountersignTask> selectTimeoutTasks();

    /**
     * 根据工作流实例ID查询所有会签任务
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 会签任务列表
     */
    List<WorkflowCountersignTask> selectByWorkflowInstanceId(@Param("workflowInstanceId") String workflowInstanceId);

    /**
     * 会签任务状态统计结果
     */
    class CountersignTaskStatusCount {
        private CountersignTaskStatus status;
        private Integer count;

        public CountersignTaskStatus getStatus() {
            return status;
        }

        public void setStatus(CountersignTaskStatus status) {
            this.status = status;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }
    }
}
