package com.gientech.workflow;

import com.gientech.GientechSystemApplication;
import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.entity.WorkflowCountersignTask;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.enums.CountersignTaskStatus;
import com.gientech.workflow.enums.InstanceStatus;
import com.gientech.workflow.enums.TaskStatus;
import com.gientech.workflow.service.IWorkflowCountersignTaskService;
import com.gientech.workflow.service.IWorkflowDefineService;
import com.gientech.workflow.service.IWorkflowInstanceService;
import com.gientech.workflow.service.IWorkflowTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * 工作流会签功能集成测试类
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = GientechSystemApplication.class)
@Transactional
public class CountersignWorkflowTest {

    @Autowired
    private IWorkflowDefineService workflowDefineService;

    @Autowired
    private IWorkflowInstanceService workflowInstanceService;

    @Autowired
    private IWorkflowTaskService workflowTaskService;

    @Autowired
    private IWorkflowCountersignTaskService countersignTaskService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    private String testBusinessKey;
    private WorkflowDefine testWorkflowDefine;
    private Map<String, Object> testBusinessVariables;

    @Before
    public void setUp() {
        // 准备测试数据
        testBusinessKey = "countersignTest";

        // 创建测试用的工作流定义
        createTestWorkflowDefine();

        // 准备业务变量
        testBusinessVariables = new HashMap<>();
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("id", "123");
        businessData.put("applicant", "张三");
        businessData.put("amount", 500000);
        businessData.put("department", "财务部");
        businessData.put("status", "0");
        testBusinessVariables.put("businessId", "business_" + System.currentTimeMillis());
        testBusinessVariables.put("businessData", businessData);

        // 会签人列表
        List<String> assignees = Arrays.asList("manager1@dept001", "manager2@dept002", "manager3@dept003");
        testBusinessVariables.put("countersignAssignees", assignees);

        // 会签配置
        Map<String, Object> countersignConfig = new HashMap<>();
        countersignConfig.put("completionType", "majority");
        countersignConfig.put("sequential", false);
        countersignConfig.put("allowDelegate", true);
        testBusinessVariables.put("countersignConfig", countersignConfig);

        log.info("测试数据准备完成 - 业务Key: {}", testBusinessKey);
    }

    @After
    public void tearDown() {
        log.info("测试完成，清理测试数据");
    }

    /**
     * 创建测试用的工作流定义
     */
    private void createTestWorkflowDefine() {
        String definition = """
                {
                   "nodeList": [
                     {
                       "id": "1",
                       "name": "开始",
                       "type": "start",
                       "assignee": "admin"
                     },
                     {
                       "id": "2",
                       "name": "会签审批",
                       "type": "countersign",
                       "assignee": "#countersignAssignees",
                       "assigneeOrgCode": "#businessData.get('department')",
                       "handler": "#businessData.put('status', 'reviewing')"
                     },
                     {
                       "id": "3",
                       "name": "审批通过",
                       "type": "end",
                       "assignee": "admin",
                       "assigneeOrgCode": "#businessData.get('department')",
                       "handler": "#businessData.put('status', 'approved')"
                     },
                     {
                       "id": "4",
                       "name": "审批拒绝",
                       "type": "end",
                       "assignee": "admin",
                       "assigneeOrgCode": "#businessData.get('department')",
                       "handler": "#businessData.put('status', 'rejected')"
                     }
                   ],
                   "edgeList": [
                     {
                       "id": "edge1",
                       "sourceId": "1",
                       "targetId": "2",
                       "condition": "1==1"
                     },
                     {
                       "id": "edge2",
                       "sourceId": "2",
                       "targetId": "3",
                       "condition": "#countersignResult == 'approved'"
                     },
                     {
                       "id": "edge3",
                       "sourceId": "2",
                       "targetId": "4",
                       "condition": "#countersignResult == 'rejected'"
                     }
                   ]
                 }
                """;

        testWorkflowDefine = new WorkflowDefine();
        testWorkflowDefine.setId("test_workflow_define_" + System.currentTimeMillis());
        testWorkflowDefine.setBusinessKey(testBusinessKey);
        testWorkflowDefine.setName("会签测试流程");
        testWorkflowDefine.setDefinition(definition);
        testWorkflowDefine.setVersion("1.0.0");
        testWorkflowDefine.setIsEffective("1");

        workflowDefineService.saveWorkflowDefine(testWorkflowDefine);
        log.info("测试工作流定义创建完成");
    }

    /**
     * 测试完整的会签工作流程 - 全部同意场景
     */
    @Test
    public void testCompleteCountersignWorkflowAllApproved() {
        log.info("开始测试完整的会签工作流程 - 全部同意场景");

        // 修改会签配置为全部同意
        Map<String, Object> countersignConfig = new HashMap<>();
        countersignConfig.put("completionType", "all");
        testBusinessVariables.put("countersignConfig", countersignConfig);

        // 1. 启动工作流实例
        String businessId = (String) testBusinessVariables.get("businessId");
        WorkflowInstance instance = workflowInstanceService.createWorkflowInstance(
                testBusinessKey,
                businessId,
                testBusinessVariables
        );
        assertNotNull("工作流实例应创建成功", instance);
        assertEquals("实例状态应为处理中", InstanceStatus.processing, instance.getStatus());

        // 2. 验证会签任务创建
        WorkflowTask mainTask = workflowTaskService.getWorkflowTask(testBusinessKey, businessId);
        assertNotNull("应该有一个主任务", mainTask);

        assertTrue("主任务应为会签任务组", mainTask.getIsCountersignGroup());
        assertEquals("主任务状态应为已创建", TaskStatus.created, mainTask.getStatus());

        // 3. 验证会签子任务创建
        List<WorkflowCountersignTask> countersignTasks =
                countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());
        assertEquals("应该创建3个会签子任务", 3, countersignTasks.size());

        // 4. 依次完成所有会签子任务
        for (int i = 0; i < countersignTasks.size(); i++) {
            WorkflowCountersignTask task = countersignTasks.get(i);

            Map<String, Object> approvalVariables = new HashMap<>();
            approvalVariables.put("approvalComment", "第" + (i + 1) + "个审批人同意");

            workflowTaskService.completeCountersignTask(
                    task.getId(),
                    "approved",
                    "同意该申请",
                    approvalVariables
            );

            log.info("完成第{}个会签任务", i + 1);
        }

        // 5. 验证主任务完成和工作流流转
        WorkflowTask updatedMainTask = workflowTaskService.getById(mainTask.getId());
        assertEquals("主任务应已完成", TaskStatus.completed, updatedMainTask.getStatus());

        // 6. 验证工作流实例状态
        WorkflowInstance updatedInstance = workflowInstanceService.getById(instance.getId());
        assertEquals("工作流实例应已完成", InstanceStatus.completed, updatedInstance.getStatus());

        log.info("完整的会签工作流程 - 全部同意场景测试通过");
    }

    /**
     * 测试会签工作流程 - 超过半数同意场景
     */
    @Test
    public void testCountersignWorkflowMajorityApproved() {
        log.info("开始测试会签工作流程 - 超过半数同意场景");

        // 1. 启动工作流实例
        String businessId = (String) testBusinessVariables.get("businessId");
        WorkflowInstance instance = workflowInstanceService.createWorkflowInstance(
                testBusinessKey,
                businessId,
                testBusinessVariables
        );

        // 2. 获取会签子任务
        WorkflowTask mainTask = workflowTaskService.getWorkflowTask(testBusinessKey, businessId);
        List<WorkflowCountersignTask> countersignTasks =
                countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());

        // 3. 完成前两个会签子任务为同意（2/3 > 1/2）
        for (int i = 0; i < 2; i++) {
            workflowTaskService.completeCountersignTask(
                    countersignTasks.get(i).getId(),
                    "approved",
                    "同意该申请",
                    new HashMap<>()
            );
        }

        // 4. 验证主任务完成（因为超过半数同意）
        WorkflowTask updatedMainTask = workflowTaskService.getById(mainTask.getId());
        assertEquals("主任务应已完成", TaskStatus.completed, updatedMainTask.getStatus());

        // 5. 验证剩余的会签任务被取消
        List<WorkflowCountersignTask> allCountersignTasks =
                countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());

        long cancelledCount = allCountersignTasks.stream()
                .filter(task -> task.getStatus() == CountersignTaskStatus.cancelled)
                .count();
        assertEquals("应该有1个任务被取消", 1, cancelledCount);

        log.info("会签工作流程 - 超过半数同意场景测试通过");
    }

    /**
     * 测试会签工作流程 - 有拒绝的场景
     */
    @Test
    public void testCountersignWorkflowWithRejection() {
        log.info("开始测试会签工作流程 - 有拒绝的场景");

        // 修改会签配置为全部同意
        Map<String, Object> countersignConfig = new HashMap<>();
        countersignConfig.put("completionType", "all");
        testBusinessVariables.put("countersignConfig", countersignConfig);

        // 1. 启动工作流实例
        String businessId = (String) testBusinessVariables.get("businessId");
        WorkflowInstance instance = workflowInstanceService.createWorkflowInstance(
                testBusinessKey,
                businessId,
                testBusinessVariables
        );

        // 2. 获取会签子任务
        WorkflowTask mainTask = workflowTaskService.getWorkflowTask(testBusinessKey, businessId);
        List<WorkflowCountersignTask> countersignTasks =
                countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());

        // 3. 第一个任务同意，第二个任务拒绝
        workflowTaskService.completeCountersignTask(
                countersignTasks.get(0).getId(),
                "approved",
                "同意该申请",
                new HashMap<>()
        );

        workflowTaskService.completeCountersignTask(
                countersignTasks.get(1).getId(),
                "rejected",
                "不同意该申请，金额过大",
                new HashMap<>()
        );

        // 4. 验证主任务未完成（因为有拒绝且策略是全部同意）
        WorkflowTask updatedMainTask = workflowTaskService.getById(mainTask.getId());
        assertNotEquals("主任务不应完成", TaskStatus.completed, updatedMainTask.getStatus());

        // 5. 完成第三个任务为同意
        workflowTaskService.completeCountersignTask(
                countersignTasks.get(2).getId(),
                "approved",
                "同意该申请",
                new HashMap<>()
        );

        // 6. 验证主任务仍未完成（因为有拒绝）
        updatedMainTask = workflowTaskService.getById(mainTask.getId());
        assertNotEquals("主任务仍不应完成", TaskStatus.completed, updatedMainTask.getStatus());

        log.info("会签工作流程 - 有拒绝的场景测试通过");
    }

    /**
     * 测试会签任务查询功能
     */
    @Test
    public void testCountersignTaskQuery() {
        log.info("开始测试会签任务查询功能");

        // 1. 启动工作流实例
        String businessId = (String) testBusinessVariables.get("businessId");
        WorkflowInstance instance = workflowInstanceService.createWorkflowInstance(
                testBusinessKey,
                businessId,
                testBusinessVariables
        );

        // 2. 获取会签子任务
        WorkflowTask mainTask = workflowTaskService.getWorkflowTask(testBusinessKey, businessId);

        // 3. 测试根据主任务ID查询会签任务
        List<WorkflowCountersignTask> countersignTasks =
                countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());
        assertEquals("应该有3个会签任务", 3, countersignTasks.size());

        // 4. 测试根据委托人查询待处理任务
        List<WorkflowCountersignTask> pendingTasks =
                countersignTaskService.getPendingCountersignTasksByAssignee("manager1", "dept001");
        assertEquals("manager1应该有1个待处理任务", 1, pendingTasks.size());
        assertTrue("任务应为活跃状态", pendingTasks.get(0).getActive());

        // 5. 测试根据工作流实例ID查询任务
        List<WorkflowCountersignTask> instanceTasks =
                countersignTaskService.getCountersignTasksByInstanceId(instance.getId());
        assertEquals("实例下应该有3个会签任务", 3, instanceTasks.size());

        // 6. 完成一个任务后再次查询
        workflowTaskService.completeCountersignTask(
                countersignTasks.get(0).getId(),
                "approved",
                "同意",
                new HashMap<>()
        );

        pendingTasks = countersignTaskService.getPendingCountersignTasksByAssignee("manager1", "dept001");
        assertEquals("manager1完成任务后应该没有待处理任务", 0, pendingTasks.size());

        log.info("会签任务查询功能测试通过");
    }

    /**
     * 测试并发完成会签任务
     * 使用独立事务管理避免事务隔离问题
     */
    @Test
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void testConcurrentCountersignCompletion() throws Exception {
        log.info("开始测试并发完成会签任务");

        // 修改会签配置为全部同意策略，避免任务被提前取消
        Map<String, Object> countersignConfig = new HashMap<>();
        countersignConfig.put("completionType", "all");
        testBusinessVariables.put("countersignConfig", countersignConfig);

        // 使用编程式事务管理创建工作流实例和会签任务
        WorkflowInstance instance = transactionTemplate.execute(status -> {
            try {
                // 1. 启动工作流实例
                String businessId = (String) testBusinessVariables.get("businessId");
                return workflowInstanceService.createWorkflowInstance(
                        testBusinessKey,
                        businessId,
                        testBusinessVariables
                );
            } catch (Exception e) {
                log.error("创建工作流实例失败", e);
                status.setRollbackOnly();
                throw new RuntimeException("创建工作流实例失败", e);
            }
        });

        assertNotNull("工作流实例应创建成功", instance);

        // 2. 在新事务中获取会签子任务，确保数据已提交
        WorkflowTask mainTask = transactionTemplate.execute(status -> {
            String businessId = (String) testBusinessVariables.get("businessId");
            return workflowTaskService.getWorkflowTask(testBusinessKey, businessId);
        });

        assertNotNull("主任务应存在", mainTask);
        assertTrue("主任务应为会签任务组", mainTask.getIsCountersignGroup());

        // 3. 获取会签子任务并验证
        List<WorkflowCountersignTask> countersignTasks = transactionTemplate.execute(status -> {
            return countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());
        });

        assertNotNull("会签任务列表不应为空", countersignTasks);
        assertEquals("应该有3个会签任务", 3, countersignTasks.size());

        // 4. 验证所有任务都能被正确查询到
        for (int i = 0; i < countersignTasks.size(); i++) {
            WorkflowCountersignTask task = countersignTasks.get(i);
            assertNotNull("会签任务ID不应为空", task.getId());
            assertEquals("会签任务状态应为待处理", CountersignTaskStatus.pending, task.getStatus());
            log.info("验证会签任务 {}: ID={}, 状态={}", i + 1, task.getId(), task.getStatus());
        }

        log.info("所有会签任务验证完成，开始并发测试");

        // 5. 创建线程池进行并发测试
        ExecutorService executor = Executors.newFixedThreadPool(3);
        List<CompletableFuture<String>> futures = new ArrayList<>();

        // 6. 并发完成会签任务
        for (int i = 0; i < countersignTasks.size(); i++) {
            final int taskIndex = i;
            final WorkflowCountersignTask task = countersignTasks.get(i);

            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 短暂延迟，模拟真实场景
                    Thread.sleep(50 + taskIndex * 20);

                    log.info("线程 {} 开始处理会签任务，任务ID: {}", taskIndex + 1, task.getId());

                    // 在独立事务中完成会签任务
                    String result = transactionTemplate.execute(status -> {
                        try {
                            Map<String, Object> variables = new HashMap<>();
                            variables.put("approver", "manager" + (taskIndex + 1));
                            variables.put("threadId", Thread.currentThread().getId());

                            workflowTaskService.completeCountersignTask(
                                    task.getId(),
                                    "approved",
                                    "并发审批同意 - " + (taskIndex + 1),
                                    variables
                            );

                            log.info("线程 {} 成功完成会签任务 {}", taskIndex + 1, task.getId());
                            return "SUCCESS";
                        } catch (Exception e) {
                            log.error("线程 {} 完成会签任务失败，任务ID: {}", taskIndex + 1, task.getId(), e);

                            // 区分不同类型的异常
                            String errorMsg = e.getMessage();
                            if (errorMsg != null) {
                                if (errorMsg.contains("会签任务不存在")) {
                                    log.warn("线程 {} 发现任务不存在，可能已被其他线程处理", taskIndex + 1);
                                    return "TASK_NOT_FOUND";
                                } else if (errorMsg.contains("状态不是待处理")) {
                                    log.warn("线程 {} 发现任务状态已变更，可能已被其他线程处理", taskIndex + 1);
                                    return "TASK_ALREADY_PROCESSED";
                                }
                            }

                            status.setRollbackOnly();
                            return "FAILED";
                        }
                    });

                    return result != null ? result : "FAILED";
                } catch (Exception e) {
                    log.error("线程 {} 执行异常", taskIndex + 1, e);
                    return "FAILED";
                }
            }, executor);

            futures.add(future);
        }

        // 7. 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );
        allFutures.get(15, TimeUnit.SECONDS);

        // 8. 验证任务执行结果
        int successCount = 0;
        int taskNotFoundCount = 0;
        int taskAlreadyProcessedCount = 0;
        int failedCount = 0;

        for (int i = 0; i < futures.size(); i++) {
            CompletableFuture<String> future = futures.get(i);
            String result = future.get();
            log.info("线程 {} 执行结果: {}", i + 1, result);

            switch (result) {
                case "SUCCESS":
                    successCount++;
                    break;
                case "TASK_NOT_FOUND":
                    taskNotFoundCount++;
                    break;
                case "TASK_ALREADY_PROCESSED":
                    taskAlreadyProcessedCount++;
                    break;
                case "FAILED":
                    failedCount++;
                    break;
            }
        }

        log.info("并发测试结果统计 - 成功: {}, 任务不存在: {}, 任务已处理: {}, 失败: {}",
                successCount, taskNotFoundCount, taskAlreadyProcessedCount, failedCount);

        // 验证结果
        assertEquals("不应该有失败的任务", 0, failedCount);
        assertEquals("在all策略下，所有任务都应该成功完成", 3, successCount);

        // 9. 等待一段时间确保所有事务都已提交
        log.info("等待事务提交完成...");
        Thread.sleep(1000);

        // 10. 验证主任务状态（带重试机制）
        WorkflowTask finalMainTask = null;
        for (int retry = 0; retry < 5; retry++) {
            finalMainTask = transactionTemplate.execute(status -> {
                return workflowTaskService.getById(mainTask.getId());
            });

            if (finalMainTask != null && finalMainTask.getStatus() == TaskStatus.completed) {
                log.info("主任务状态验证成功，重试次数: {}", retry);
                break;
            }

            if (retry < 4) {
                log.info("主任务状态为: {}，等待重试...", finalMainTask != null ? finalMainTask.getStatus() : "null");
                Thread.sleep(500);
            }
        }

        assertNotNull("主任务应存在", finalMainTask);
        assertEquals("主任务应已完成", TaskStatus.completed, finalMainTask.getStatus());

        // 11. 验证会签任务最终状态
        List<WorkflowCountersignTask> finalCountersignTasks = transactionTemplate.execute(status -> {
            return countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());
        });

        long approvedCount = finalCountersignTasks.stream()
                .filter(task -> task.getStatus() == CountersignTaskStatus.approved)
                .count();

        assertEquals("所有会签任务都应该被同意", 3, approvedCount);

        // 12. 清理测试数据
        try {
            transactionTemplate.execute(status -> {
                // 删除会签任务
                countersignTaskService.remove(
                    new LambdaQueryWrapper<WorkflowCountersignTask>()
                        .eq(WorkflowCountersignTask::getWorkflowTaskId, mainTask.getId())
                );

                // 删除主任务
                workflowTaskService.removeById(mainTask.getId());

                // 删除工作流实例
                workflowInstanceService.removeById(instance.getId());

                // 删除工作流定义
                workflowDefineService.removeById(testWorkflowDefine.getId());

                return null;
            });
            log.info("测试数据清理完成");
        } catch (Exception e) {
            log.warn("清理测试数据时出现异常", e);
        } finally {
            // 确保线程池被关闭
            if (executor != null && !executor.isShutdown()) {
                executor.shutdown();
                try {
                    if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                        executor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }

        log.info("并发完成会签任务测试通过");
    }

    /**
     * 测试大量会签人的性能
     */
    @Test
    public void testLargeScaleCountersign() {
        log.info("开始测试大量会签人的性能");

        // 1. 准备大量会签人（10个）
        List<String> largeAssigneeList = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            largeAssigneeList.add("user" + i + "@dept" + String.format("%03d", i));
        }

        Map<String, Object> largeScaleVariables = new HashMap<>(testBusinessVariables);
        largeScaleVariables.put("countersignAssignees", largeAssigneeList);

        // 配置为任意一个同意即可完成
        Map<String, Object> countersignConfig = new HashMap<>();
        countersignConfig.put("completionType", "any");
        largeScaleVariables.put("countersignConfig", countersignConfig);

        // 2. 记录开始时间
        long startTime = System.currentTimeMillis();

        // 3. 启动工作流实例
        String businessId = (String) largeScaleVariables.get("businessId");
        WorkflowInstance instance = workflowInstanceService.createWorkflowInstance(
                testBusinessKey,
                businessId,
                largeScaleVariables
        );

        // 4. 记录创建时间
        long createTime = System.currentTimeMillis();
        log.info("创建10个会签任务耗时: {}ms", createTime - startTime);

        // 5. 获取会签子任务
        WorkflowTask mainTask = workflowTaskService.getWorkflowTask(testBusinessKey, businessId);
        List<WorkflowCountersignTask> countersignTasks =
                countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());

        assertEquals("应该创建10个会签任务", 10, countersignTasks.size());

//        Map<String, Object> countersignVariables = new HashMap<>();
//        countersignVariables.put("approve", true);

        // 6. 完成第一个会签任务
        workflowTaskService.completeCountersignTask(
                countersignTasks.get(0).getId(),
                "approved",
                "同意",
                new HashMap<>()
        );

        // 7. 记录完成时间
        long completeTime = System.currentTimeMillis();
        log.info("完成会签任务并流转耗时: {}ms", completeTime - createTime);

        // 8. 验证主任务完成
        WorkflowTask updatedMainTask = workflowTaskService.getById(mainTask.getId());
        assertEquals("主任务应已完成", TaskStatus.completed, updatedMainTask.getStatus());

        // 9. 验证性能指标
        long totalTime = completeTime - startTime;
        assertTrue("总耗时应在合理范围内（小于5秒）", totalTime < 5000);

        log.info("大量会签人的性能测试通过，总耗时: {}ms", totalTime);
    }

    /**
     * 测试异常场景 - 无效的会签配置
     */
    @Test
    public void testInvalidCountersignConfig() {
        log.info("开始测试异常场景 - 无效的会签配置");

        // 1. 准备无效的会签配置
        Map<String, Object> invalidVariables = new HashMap<>(testBusinessVariables);

        // 空的会签人列表
        invalidVariables.put("countersignAssignees", new ArrayList<>());

        try {
            // 2. 尝试启动工作流实例
            String businessId = (String) invalidVariables.get("businessId");
            WorkflowInstance instance = workflowInstanceService.createWorkflowInstance(
                    testBusinessKey,
                    businessId,
                    invalidVariables
            );

            // 3. 验证会签任务创建情况
            WorkflowTask mainTask = workflowTaskService.getWorkflowTask(testBusinessKey, businessId);
            if (mainTask != null) {
                List<WorkflowCountersignTask> countersignTasks =
                        countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());

                assertEquals("空会签人列表应该创建0个会签任务", 0, countersignTasks.size());
            }

            log.info("异常场景 - 无效的会签配置测试通过");
        } catch (Exception e) {
            log.info("预期的异常: {}", e.getMessage());
            // 这是预期的异常，测试通过
        }
    }

    /**
     * 测试事务回滚场景
     */
    @Test
    public void testTransactionRollback() {
        log.info("开始测试事务回滚场景");

        // 1. 启动工作流实例
        String businessId = (String) testBusinessVariables.get("businessId");
        WorkflowInstance instance = workflowInstanceService.createWorkflowInstance(
                testBusinessKey,
                businessId,
                testBusinessVariables
        );

        // 2. 获取会签子任务
        WorkflowTask mainTask = workflowTaskService.getWorkflowTask(testBusinessKey, businessId);
        List<WorkflowCountersignTask> countersignTasks =
                countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());

        // 3. 记录初始状态
        int initialPendingCount = (int) countersignTasks.stream()
                .filter(task -> task.getStatus() == CountersignTaskStatus.pending)
                .count();

        try {
            // 4. 尝试完成一个不存在的会签任务（应该失败）
            workflowTaskService.completeCountersignTask(
                    "non_existent_task_id",
                    "approved",
                    "测试",
                    new HashMap<>()
            );

            fail("应该抛出异常");
        } catch (Exception e) {
            log.info("预期的异常: {}", e.getMessage());
        }

        // 5. 验证数据没有被修改（事务回滚）
        List<WorkflowCountersignTask> afterFailureTasks =
                countersignTaskService.getCountersignTasksByWorkflowTaskId(mainTask.getId());

        int afterFailurePendingCount = (int) afterFailureTasks.stream()
                .filter(task -> task.getStatus() == CountersignTaskStatus.pending)
                .count();

        assertEquals("失败后待处理任务数量应该不变", initialPendingCount, afterFailurePendingCount);

        log.info("事务回滚场景测试通过");
    }
}
