package com.gientech.workflow.factory.impl;

import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.enums.NodeType;
import com.gientech.workflow.enums.TaskStatus;
import com.gientech.workflow.factory.WorkflowTaskFactory;
import com.gientech.workflow.parser.SpringELParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 会签任务工厂
 * 负责创建会签任务类型的工作流任务
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
@Slf4j
@Component
public class CountersignTaskFactory implements WorkflowTaskFactory {

    @Autowired
    private SpringELParser springElParser;

    @Override
    public WorkflowTask createTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables) {
        if (!supports(node)) {
            return null;
        }

        log.info("创建会签任务: 节点ID={}, 节点名称={}", node.getId(), node.getName());

        // 解析会签配置
        Map<String, Object> countersignConfig = parseCountersignConfig(node, variables);

        return WorkflowTask.builder()
                .workflowInstanceId(instance.getId())
                .name(node.getName())
                .status(TaskStatus.created)
                .assignee(evaluateAssignee(node, variables))
                .assigneeOrgCode(evaluateAssigneeOrgCode(node, variables))
                .isCountersignGroup(true)
                .countersignConfig(countersignConfig)
                .variables(variables)
                .build();
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.countersign.equals(node.getType());
    }

    /**
     * 解析委托人
     */
    private String evaluateAssignee(WorkflowNode node, Map<String, Object> variables) {
        if (node.getAssignee() == null) {
            log.warn("会签节点 {} 的委托人为空", node.getId());
            return "";
        }
        try {
            Object result = springElParser.evaluateExpression(node.getAssignee(), variables);
            return result != null ? result.toString() : "";
        } catch (Exception e) {
            log.error("解析委托人表达式失败: {}", node.getAssignee(), e);
            return "";
        }
    }

    /**
     * 解析委托人机构
     */
    private String evaluateAssigneeOrgCode(WorkflowNode node, Map<String, Object> variables) {
        if (node.getAssigneeOrgCode() == null) {
            log.warn("会签节点 {} 的委托人机构为空", node.getId());
            return "";
        }
        try {
            Object result = springElParser.evaluateExpression(node.getAssigneeOrgCode(), variables);
            return result != null ? result.toString() : "";
        } catch (Exception e) {
            log.error("解析委托人机构表达式失败: {}", node.getAssigneeOrgCode(), e);
            return "";
        }
    }

    /**
     * 解析会签配置
     * 从节点定义或业务变量中获取会签配置信息
     */
    private Map<String, Object> parseCountersignConfig(WorkflowNode node, Map<String, Object> variables) {
        Map<String, Object> config = new HashMap<>();

        // 默认配置
        config.put("completionType", "all"); // 完成类型：all(全部同意), any(任意一个同意), majority(超过半数), percentage(按百分比)
        config.put("requiredPercentage", 100.0); // 当completionType为percentage时的百分比要求
        config.put("sequential", false); // 是否顺序会签
        config.put("allowDelegate", true); // 是否允许委托
        config.put("timeoutAction", "escalate"); // 超时处理：escalate(升级), auto_approve(自动同意), auto_reject(自动拒绝)

        // 从业务变量中获取会签配置
        Object countersignConfigFromVar = variables.get("countersignConfig");
        if (countersignConfigFromVar instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> configFromVar = (Map<String, Object>) countersignConfigFromVar;
            config.putAll(configFromVar);
        }

        // 从节点属性中获取会签配置（如果节点定义中包含扩展属性）
        // 这里可以根据实际的节点定义结构来解析

        log.debug("会签配置: {}", config);
        return config;
    }
}
