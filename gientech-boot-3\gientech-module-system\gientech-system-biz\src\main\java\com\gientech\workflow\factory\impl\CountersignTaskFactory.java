package com.gientech.workflow.factory.impl;

import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.enums.NodeType;
import com.gientech.workflow.enums.TaskStatus;
import com.gientech.workflow.factory.WorkflowTaskFactory;
import com.gientech.workflow.parser.SpringELParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 会签任务工厂
 * 负责创建会签任务类型的工作流任务
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
@Slf4j
@Component
public class CountersignTaskFactory implements WorkflowTaskFactory {

    @Autowired
    private SpringELParser springElParser;

    @Override
    public WorkflowTask createTask(WorkflowInstance instance, WorkflowNode node, Map<String, Object> variables) {
        if (!supports(node)) {
            return null;
        }

        log.info("创建会签任务: 节点ID={}, 节点名称={}", node.getId(), node.getName());

        // 简化会签配置：固定为全部通过模式
        Map<String, Object> countersignConfig = new HashMap<>();
        countersignConfig.put("completionType", "all");

        return WorkflowTask.builder()
                .workflowInstanceId(instance.getId())
                .name(node.getName())
                .status(TaskStatus.created)
                .assignee(evaluateAssignee(node, variables))
                .assigneeOrgCode(evaluateAssigneeOrgCode(node, variables))
                .isCountersignGroup(true)
                .countersignConfig(countersignConfig)
                .variables(variables)
                .build();
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.countersign.equals(node.getType());
    }

    /**
     * 解析委托人
     */
    private String evaluateAssignee(WorkflowNode node, Map<String, Object> variables) {
        if (node.getAssignee() == null) {
            log.warn("会签节点 {} 的委托人为空", node.getId());
            return "";
        }
        try {
            Object result = springElParser.evaluateExpression(node.getAssignee(), variables);
            return result != null ? result.toString() : "";
        } catch (Exception e) {
            log.error("解析委托人表达式失败: {}", node.getAssignee(), e);
            return "";
        }
    }

    /**
     * 解析委托人机构
     */
    private String evaluateAssigneeOrgCode(WorkflowNode node, Map<String, Object> variables) {
        if (node.getAssigneeOrgCode() == null) {
            log.warn("会签节点 {} 的委托人机构为空", node.getId());
            return "";
        }
        try {
            Object result = springElParser.evaluateExpression(node.getAssigneeOrgCode(), variables);
            return result != null ? result.toString() : "";
        } catch (Exception e) {
            log.error("解析委托人机构表达式失败: {}", node.getAssigneeOrgCode(), e);
            return "";
        }
    }


}
