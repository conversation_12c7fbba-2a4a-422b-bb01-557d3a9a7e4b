package com.gientech.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gientech.workflow.entity.WorkflowCountersignTask;
import com.gientech.workflow.enums.CountersignTaskStatus;
import com.gientech.workflow.mapper.WorkflowCountersignTaskMapper.CountersignTaskStatusCount;

import java.util.List;
import java.util.Map;

/**
 * 工作流会签任务 服务接口
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
public interface IWorkflowCountersignTaskService extends IService<WorkflowCountersignTask> {

    /**
     * 创建会签子任务
     *
     * @param workflowTaskId 主任务ID
     * @param workflowInstanceId 工作流实例ID
     * @param assignees 会签人列表
     * @param taskName 任务名称
     * @param variables 业务变量
     * @return 创建的会签子任务列表
     */
    List<WorkflowCountersignTask> createCountersignTasks(String workflowTaskId, String workflowInstanceId,
                                                         List<String> assignees, String taskName,
                                                         Map<String, Object> variables);

    /**
     * 完成会签子任务
     *
     * @param countersignTaskId 会签子任务ID
     * @param status 完成状态（approved/rejected）
     * @param comment 审批意见
     * @param variables 业务变量
     * @return 是否成功
     */
    boolean completeCountersignTask(String countersignTaskId, CountersignTaskStatus status,
                                    String comment, Map<String, Object> variables);

    /**
     * 检查主任务下的所有会签子任务是否已完成
     *
     * @param workflowTaskId 主任务ID
     * @return 是否全部完成
     */
    boolean isAllCountersignTasksCompleted(String workflowTaskId);

    /**
     * 获取主任务下的会签子任务统计信息
     *
     * @param workflowTaskId 主任务ID
     * @return 统计信息
     */
    List<CountersignTaskStatusCount> getCountersignTaskStatistics(String workflowTaskId);

    /**
     * 根据主任务ID查询所有会签子任务
     *
     * @param workflowTaskId 主任务ID
     * @return 会签子任务列表
     */
    List<WorkflowCountersignTask> getCountersignTasksByWorkflowTaskId(String workflowTaskId);

    /**
     * 根据委托人查询待处理的会签任务
     *
     * @param assignee 委托人
     * @param assigneeOrgCode 委托人机构
     * @return 待处理的会签任务列表
     */
    List<WorkflowCountersignTask> getPendingCountersignTasksByAssignee(String assignee, String assigneeOrgCode);

    /**
     * 取消主任务下的所有待处理会签子任务
     *
     * @param workflowTaskId 主任务ID
     * @param cancelReason 取消原因
     * @return 取消的任务数量
     */
    int cancelPendingCountersignTasks(String workflowTaskId, String cancelReason);

    /**
     * 处理超时的会签任务
     *
     * @return 处理的任务数量
     */
    int handleTimeoutCountersignTasks();

    /**
     * 委托会签任务
     *
     * @param countersignTaskId 会签子任务ID
     * @param delegateToAssignee 被委托人
     * @param delegateToOrgCode 被委托人机构
     * @param delegateReason 委托原因
     * @return 是否成功
     */
    boolean delegateCountersignTask(String countersignTaskId, String delegateToAssignee,
                                    String delegateToOrgCode, String delegateReason);

    /**
     * 根据工作流实例ID查询所有会签任务
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 会签任务列表
     */
    List<WorkflowCountersignTask> getCountersignTasksByInstanceId(String workflowInstanceId);

    /**
     * 检查会签任务是否可以完成主任务
     * 根据会签配置判断是否满足完成条件
     *
     * @param workflowTaskId 主任务ID
     * @param countersignConfig 会签配置
     * @return 是否可以完成主任务
     */
    boolean canCompleteMainTask(String workflowTaskId, Map<String, Object> countersignConfig);
}
