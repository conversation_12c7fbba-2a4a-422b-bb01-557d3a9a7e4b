package com.gientech.workflow;

import com.gientech.workflow.service.WorkflowCountersignTaskServiceTest;
import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * 会签功能测试套件
 * 运行所有会签相关的测试用例
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    WorkflowCountersignTaskServiceTest.class,
    CountersignWorkflowTest.class
})
public class CountersignTestSuite {
    // 测试套件类，用于批量运行所有会签相关测试
}
