package com.gientech.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gientech.workflow.entity.WorkflowCountersignTask;
import com.gientech.workflow.enums.CountersignTaskStatus;
import com.gientech.workflow.mapper.WorkflowCountersignTaskMapper;
import com.gientech.workflow.mapper.WorkflowCountersignTaskMapper.CountersignTaskStatusCount;
import com.gientech.workflow.service.IWorkflowCountersignTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 工作流会签任务 服务实现类
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
@Slf4j
@Service
public class WorkflowCountersignTaskServiceImpl extends ServiceImpl<WorkflowCountersignTaskMapper, WorkflowCountersignTask>
        implements IWorkflowCountersignTaskService {

    @Autowired
    private WorkflowCountersignTaskMapper countersignTaskMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WorkflowCountersignTask> createCountersignTasks(String workflowTaskId, String workflowInstanceId,
                                                                List<String> assignees, String taskName,
                                                                Map<String, Object> variables) {
        if (CollectionUtils.isEmpty(assignees)) {
            log.warn("会签人列表为空，无法创建会签任务");
            return Collections.emptyList();
        }

        log.info("开始创建会签任务，主任务ID: {}, 会签人数量: {}", workflowTaskId, assignees.size());

        List<WorkflowCountersignTask> countersignTasks = new ArrayList<>();

        for (int i = 0; i < assignees.size(); i++) {
            String assignee = assignees.get(i);
            if (!StringUtils.hasText(assignee)) {
                log.warn("跳过空的会签人");
                continue;
            }

            // 解析会签人和机构（格式可能是 "user@orgCode" 或 "user"）
            String[] assigneeParts = assignee.split("@");
            String actualAssignee = assigneeParts[0];
            String assigneeOrgCode = assigneeParts.length > 1 ? assigneeParts[1] : null;

            WorkflowCountersignTask countersignTask = WorkflowCountersignTask.builder()
                    .workflowTaskId(workflowTaskId)
                    .workflowInstanceId(workflowInstanceId)
                    .name(taskName + " - 会签任务" + (i + 1))
                    .assignee(actualAssignee)
                    .assigneeOrgCode(assigneeOrgCode)
                    .status(CountersignTaskStatus.pending)
                    .sequence(i + 1)
                    .active(true) // 设置为活跃
                    .properties(new HashMap<>(variables))
                    .build();

            countersignTasks.add(countersignTask);
        }

        // 批量保存
        this.saveBatch(countersignTasks);

        log.info("成功创建 {} 个会签任务", countersignTasks.size());
        return countersignTasks;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeCountersignTask(String countersignTaskId, CountersignTaskStatus status,
                                           String comment, Map<String, Object> variables) {
        log.info("开始完成会签任务，任务ID: {}, 状态: {}", countersignTaskId, status);

        // 查询会签任务（添加重试机制以处理并发场景）
        WorkflowCountersignTask countersignTask = baseMapper.selectById(countersignTaskId);

        if (countersignTask == null) {
            log.error("会签任务不存在，任务ID: {}，可能已被其他线程处理", countersignTaskId);
            return false;
        }

        if (countersignTask.getStatus() != CountersignTaskStatus.pending) {
            log.warn("会签任务状态不是待处理，无法完成，当前状态: {}，任务ID: {}",
                    countersignTask.getStatus(), countersignTaskId);
            return false;
        }

        // 更新会签任务状态
        countersignTask.setStatus(status);
        countersignTask.setComment(comment);
        countersignTask.setApprovalTime(new Date());
        countersignTask.setActive(false);

        // 合并业务变量
        if (variables != null && !variables.isEmpty()) {
            Map<String, Object> properties = countersignTask.getProperties();
            if (properties == null) {
                properties = new HashMap<>();
            }
            properties.putAll(variables);
            countersignTask.setProperties(properties);
        }

        boolean updateResult = this.updateById(countersignTask);

        if (updateResult) {
            log.info("会签任务完成成功，任务ID: {}, 状态: {}", countersignTaskId, status);
        }

        return updateResult;
    }

    @Override
    public boolean isAllCountersignTasksCompleted(String workflowTaskId) {
        LambdaQueryWrapper<WorkflowCountersignTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkflowCountersignTask::getWorkflowTaskId, workflowTaskId)
                .eq(WorkflowCountersignTask::getDelFlag, 0)
                .eq(WorkflowCountersignTask::getStatus, CountersignTaskStatus.pending);

        long pendingCount = this.count(queryWrapper);

        log.debug("主任务 {} 下还有 {} 个待处理的会签任务", workflowTaskId, pendingCount);
        return pendingCount == 0;
    }

    @Override
    public List<CountersignTaskStatusCount> getCountersignTaskStatistics(String workflowTaskId) {
        return countersignTaskMapper.countByWorkflowTaskIdGroupByStatus(workflowTaskId);
    }

    @Override
    public List<WorkflowCountersignTask> getCountersignTasksByWorkflowTaskId(String workflowTaskId) {
        return countersignTaskMapper.selectByWorkflowTaskId(workflowTaskId);
    }

    @Override
    public List<WorkflowCountersignTask> getPendingCountersignTasksByAssignee(String assignee, String assigneeOrgCode) {
        return countersignTaskMapper.selectPendingTasksByAssignee(assignee, assigneeOrgCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelPendingCountersignTasks(String workflowTaskId, String cancelReason) {
        log.info("开始取消主任务下的待处理会签任务，主任务ID: {}", workflowTaskId);

        LambdaUpdateWrapper<WorkflowCountersignTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkflowCountersignTask::getWorkflowTaskId, workflowTaskId)
                .eq(WorkflowCountersignTask::getStatus, CountersignTaskStatus.pending)
                .set(WorkflowCountersignTask::getStatus, CountersignTaskStatus.cancelled)
                .set(WorkflowCountersignTask::getComment, cancelReason)
                .set(WorkflowCountersignTask::getActive, false)
                .set(WorkflowCountersignTask::getUpdateTime, new Date());

        int cancelledCount = baseMapper.update(null, updateWrapper);
        log.info("成功取消 {} 个会签任务", cancelledCount);

        return cancelledCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int handleTimeoutCountersignTasks() {
        List<WorkflowCountersignTask> timeoutTasks = countersignTaskMapper.selectTimeoutTasks();

        if (CollectionUtils.isEmpty(timeoutTasks)) {
            return 0;
        }

        log.info("发现 {} 个超时的会签任务", timeoutTasks.size());

        List<String> taskIds = timeoutTasks.stream()
                .map(WorkflowCountersignTask::getId)
                .toList();

        int updatedCount = countersignTaskMapper.batchUpdateStatus(taskIds, CountersignTaskStatus.timeout, "SYSTEM");

        log.info("成功处理 {} 个超时的会签任务", updatedCount);
        return updatedCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delegateCountersignTask(String countersignTaskId, String delegateToAssignee,
                                           String delegateToOrgCode, String delegateReason) {
        log.info("开始委托会签任务，任务ID: {}, 委托给: {}", countersignTaskId, delegateToAssignee);

        WorkflowCountersignTask countersignTask = this.getById(countersignTaskId);
        if (countersignTask == null || countersignTask.getStatus() != CountersignTaskStatus.pending) {
            log.error("会签任务不存在或状态不正确，无法委托");
            return false;
        }

        // 更新原任务状态
        countersignTask.setStatus(CountersignTaskStatus.delegated);
        countersignTask.setComment(delegateReason);
        countersignTask.setActive(false);

        boolean updateResult = this.updateById(countersignTask);

        if (updateResult) {
            // 创建新的委托任务
            WorkflowCountersignTask delegatedTask = WorkflowCountersignTask.builder()
                    .workflowTaskId(countersignTask.getWorkflowTaskId())
                    .workflowInstanceId(countersignTask.getWorkflowInstanceId())
                    .name(countersignTask.getName() + " (委托)")
                    .assignee(delegateToAssignee)
                    .assigneeOrgCode(delegateToOrgCode)
                    .status(CountersignTaskStatus.pending)
                    .sequence(countersignTask.getSequence())
                    .active(true)
                    .delegateFrom(countersignTask.getAssignee())
                    .properties(countersignTask.getProperties())
                    .build();

            this.save(delegatedTask);
            log.info("会签任务委托成功");
        }

        return updateResult;
    }

    @Override
    public List<WorkflowCountersignTask> getCountersignTasksByInstanceId(String workflowInstanceId) {
        return countersignTaskMapper.selectByWorkflowInstanceId(workflowInstanceId);
    }

    @Override
    public boolean canCompleteMainTask(String workflowTaskId, Map<String, Object> countersignConfig) {
        log.debug("检查主任务 {} 是否可以完成", workflowTaskId);

        // 获取会签任务统计信息
        List<CountersignTaskStatusCount> statistics = getCountersignTaskStatistics(workflowTaskId);

        if (CollectionUtils.isEmpty(statistics)) {
            log.warn("主任务 {} 下没有会签任务", workflowTaskId);
            return true;
        }

        // 统计各状态的任务数量
        Map<CountersignTaskStatus, Integer> statusCountMap = new HashMap<>();
        int totalCount = 0;
        for (CountersignTaskStatusCount stat : statistics) {
            statusCountMap.put(stat.getStatus(), stat.getCount());
            totalCount += stat.getCount();
            log.debug("会签任务状态统计: {} = {}", stat.getStatus(), stat.getCount());
        }

        int approvedCount = statusCountMap.getOrDefault(CountersignTaskStatus.approved, 0);
        int rejectedCount = statusCountMap.getOrDefault(CountersignTaskStatus.rejected, 0);
        int pendingCount = statusCountMap.getOrDefault(CountersignTaskStatus.pending, 0);
        int cancelledCount = statusCountMap.getOrDefault(CountersignTaskStatus.cancelled, 0);

        log.info("主任务 {} 会签统计 - 总数: {}, 同意: {}, 拒绝: {}, 待处理: {}, 已取消: {}",
                workflowTaskId, totalCount, approvedCount, rejectedCount, pendingCount, cancelledCount);

        // 解析会签配置
        String completionType = (String) countersignConfig.getOrDefault("completionType", "all");
        log.debug("会签完成策略: {}", completionType);

        boolean canComplete = false;
        switch (completionType) {
            case "all":
                // 全部同意才能完成（不包括已取消的任务）
                canComplete = pendingCount == 0 && rejectedCount == 0 && approvedCount > 0;
                log.debug("all策略判断: pendingCount={}, rejectedCount={}, approvedCount={}, 结果={}",
                        pendingCount, rejectedCount, approvedCount, canComplete);
                break;
            case "any":
                // 任意一个同意就能完成
                canComplete = approvedCount > 0;
                log.debug("any策略判断: approvedCount={}, 结果={}", approvedCount, canComplete);
                break;
            case "majority":
                // 超过半数同意就能完成
                canComplete = approvedCount > totalCount / 2;
                log.debug("majority策略判断: approvedCount={}, totalCount/2={}, 结果={}",
                        approvedCount, totalCount / 2, canComplete);
                break;
            case "percentage":
                // 按百分比计算
                double requiredPercentage = (Double) countersignConfig.getOrDefault("requiredPercentage", 100.0);
                double approvedPercentage = (double) approvedCount / totalCount * 100;
                canComplete = approvedPercentage >= requiredPercentage;
                log.debug("percentage策略判断: approvedPercentage={}, requiredPercentage={}, 结果={}",
                        approvedPercentage, requiredPercentage, canComplete);
                break;
            default:
                log.warn("未知的会签完成类型: {}", completionType);
                canComplete = pendingCount == 0 && rejectedCount == 0 && approvedCount > 0;
                break;
        }

        log.info("主任务 {} 完成条件检查结果: {}", workflowTaskId, canComplete);
        return canComplete;
    }
}
