package com.gientech.workflow.strategy.impl;

import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.define.WorkflowNode;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.enums.InstanceStatus;
import com.gientech.workflow.enums.NodeType;
import com.gientech.workflow.factory.WorkflowTaskFactoryManager;
import com.gientech.workflow.mapper.WorkflowTaskMapper;
import com.gientech.workflow.service.IWorkflowCountersignTaskService;
import com.gientech.workflow.strategy.AbstractNodeExecutionStrategy;
import com.gientech.workflow.strategy.NodeExecutionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 会签节点执行策略
 * 负责处理会签节点的执行逻辑
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
@Slf4j
@Component
public class CountersignNodeExecutionStrategy extends AbstractNodeExecutionStrategy {

    @Autowired
    private WorkflowTaskFactoryManager taskFactoryManager;

    @Autowired
    private WorkflowTaskMapper workflowTaskMapper;

    @Autowired
    private IWorkflowCountersignTaskService countersignTaskService;

    @Override
    public NodeExecutionResult execute(WorkflowInstance instance, WorkflowNode node,
                                       WorkflowDefine define, Map<String, Object> variables,
                                       WorkflowTask task) {
        try {
            log.info("执行会签节点: 节点ID={}, 节点名称={}", node.getId(), node.getName());

            // 执行业务操作
            executeBusinessHandler(node, define, variables);

            // 创建会签主任务
            WorkflowTask countersignTask = taskFactoryManager.createTask(instance, node, variables);
            if (countersignTask == null) {
                return NodeExecutionResult.failure("创建会签任务失败");
            }
            workflowTaskMapper.insert(countersignTask);

            // 解析会签人列表
            List<String> assignees = parseAssignees(node, variables);
            if (assignees.isEmpty()) {
                log.warn("会签人列表为空，节点ID: {}，将创建空的会签任务组", node.getId());
                // 会签人列表为空时，仍然创建主任务，但不创建会签子任务
                // 这种情况下，主任务可以直接完成
            } else {
                log.info("会签人列表: {}", assignees);

                // 创建会签子任务
                countersignTaskService.createCountersignTasks(
                        countersignTask.getId(),
                        instance.getId(),
                        assignees,
                        node.getName(),
                        variables
                );
            }

            return NodeExecutionResult.success(
                    "会签任务创建成功: " + node.getName(),
                    countersignTask,
                    InstanceStatus.processing
            );

        } catch (Exception e) {
            log.error("执行会签节点失败: 节点ID={}", node.getId(), e);
            return NodeExecutionResult.failure("执行会签节点失败: " + e.getMessage());
        }
    }

    @Override
    public boolean supports(WorkflowNode node) {
        return NodeType.countersign.equals(node.getType());
    }

    /**
     * 解析会签人列表
     * 从节点定义或业务变量中获取会签人信息
     */
    private List<String> parseAssignees(WorkflowNode node, Map<String, Object> variables) {
        // 优先从业务变量中获取会签人列表
        Object assigneesFromVar = variables.get("countersignAssignees");
        if (assigneesFromVar instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> assigneesList = (List<String>) assigneesFromVar;
            return assigneesList;
        }

        if (assigneesFromVar instanceof String assigneesStr) {
            if (StringUtils.hasText(assigneesStr)) {
                // 支持逗号分隔的字符串格式
                return Arrays.asList(assigneesStr.split(","));
            }
        }

        // 从节点定义中获取会签人（如果节点的assignee字段包含多个会签人）
        if (StringUtils.hasText(node.getAssignee())) {
            String assigneeStr = node.getAssignee();
            // 如果包含逗号，则认为是多个会签人
            if (assigneeStr.contains(",")) {
                return Arrays.asList(assigneeStr.split(","));
            } else {
                return List.of(assigneeStr);
            }
        }

        log.warn("未找到会签人配置，节点ID: {}", node.getId());
        return List.of();
    }
}
