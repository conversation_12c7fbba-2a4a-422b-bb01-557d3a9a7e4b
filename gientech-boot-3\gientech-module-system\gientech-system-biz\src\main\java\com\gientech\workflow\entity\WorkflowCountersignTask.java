package com.gientech.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gientech.workflow.enums.CountersignTaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 工作流会签任务
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
@Data
@TableName(value = "workflow_countersign_task", autoResultMap = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工作流会签任务")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowCountersignTask implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记")
    private Integer delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;

    /**
     * 关联的主任务ID
     */
    @Schema(description = "关联的主任务ID")
    private String workflowTaskId;

    /**
     * 工作流实例ID
     */
    @Schema(description = "工作流实例ID")
    private String workflowInstanceId;

    /**
     * 会签任务名称
     */
    @Schema(description = "会签任务名称")
    private String name;

    /**
     * 会签人
     */
    @Schema(description = "会签人")
    private String assignee;

    /**
     * 会签人机构
     */
    @Schema(description = "会签人机构")
    private String assigneeOrgCode;

    /**
     * 会签任务状态
     */
    @Schema(description = "会签任务状态")
    private CountersignTaskStatus status;

    /**
     * 审批意见
     */
    @Schema(description = "审批意见")
    private String comment;

    /**
     * 审批时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审批时间")
    private Date approvalTime;

    /**
     * 会签顺序
     */
    @Schema(description = "会签顺序")
    private Integer sequence;

    /**
     * 是否为当前活跃任务
     */
    @Schema(description = "是否为当前活跃任务")
    private Boolean active;

    /**
     * 委托人
     */
    @Schema(description = "委托人")
    private String delegateFrom;

//    /**
//     * 任务超时时间
//     */
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Schema(description = "任务超时时间")
//    private Date timeoutTime;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private Map<String, Object> properties = new HashMap<>();
}
