<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gientech.workflow.mapper.WorkflowCountersignTaskMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gientech.workflow.entity.WorkflowCountersignTask">
        <id column="id" property="id"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="workflow_task_id" property="workflowTaskId"/>
        <result column="workflow_instance_id" property="workflowInstanceId"/>
        <result column="name" property="name"/>
        <result column="assignee" property="assignee"/>
        <result column="assignee_org_code" property="assigneeOrgCode"/>
        <result column="status" property="status"/>
        <result column="comment" property="comment"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="sequence" property="sequence"/>
        <result column="active" property="active"/>
        <result column="delegate_from" property="delegateFrom"/>
        <result column="timeout_time" property="timeoutTime"/>
        <result column="properties" property="properties" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <!-- 状态统计结果映射 -->
    <resultMap id="StatusCountResultMap" type="com.gientech.workflow.mapper.WorkflowCountersignTaskMapper$CountersignTaskStatusCount">
        <result column="status" property="status"/>
        <result column="count" property="count"/>
    </resultMap>

    <!-- 根据主任务ID查询所有会签子任务 -->
    <select id="selectByWorkflowTaskId" resultMap="BaseResultMap">
        SELECT * FROM workflow_countersign_task
        WHERE workflow_task_id = #{workflowTaskId}
        AND del_flag = 0
        ORDER BY sequence ASC, create_time ASC
    </select>

    <!-- 根据主任务ID和状态查询会签子任务 -->
    <select id="selectByWorkflowTaskIdAndStatus" resultMap="BaseResultMap">
        SELECT * FROM workflow_countersign_task
        WHERE workflow_task_id = #{workflowTaskId}
        AND status = #{status}
        AND del_flag = 0
        ORDER BY sequence ASC, create_time ASC
    </select>

    <!-- 根据委托人查询待处理的会签任务 -->
    <select id="selectPendingTasksByAssignee" resultMap="BaseResultMap">
        SELECT * FROM workflow_countersign_task
        WHERE assignee = #{assignee}
        <if test="assigneeOrgCode != null and assigneeOrgCode != ''">
            AND assignee_org_code = #{assigneeOrgCode}
        </if>
        AND status = 'pending'
        AND active = true
        AND del_flag = 0
        ORDER BY create_time ASC
    </select>

    <!-- 统计主任务下各状态的会签子任务数量 -->
    <select id="countByWorkflowTaskIdGroupByStatus" resultMap="StatusCountResultMap">
        SELECT status, COUNT(*) as count
        FROM workflow_countersign_task
        WHERE workflow_task_id = #{workflowTaskId}
        AND del_flag = 0
        GROUP BY status
    </select>

    <!-- 批量更新会签任务状态 -->
    <update id="batchUpdateStatus">
        UPDATE workflow_countersign_task
        SET status = #{status},
            update_by = #{updateBy},
            update_time = NOW()
        WHERE id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND del_flag = 0
    </update>

    <!-- 查询超时的会签任务 -->
    <select id="selectTimeoutTasks" resultMap="BaseResultMap">
        SELECT * FROM workflow_countersign_task
        WHERE status = 'pending'
        AND timeout_time IS NOT NULL
        AND timeout_time &lt; NOW()
        AND del_flag = 0
        ORDER BY timeout_time ASC
    </select>

    <!-- 根据工作流实例ID查询所有会签任务 -->
    <select id="selectByWorkflowInstanceId" resultMap="BaseResultMap">
        SELECT * FROM workflow_countersign_task
        WHERE workflow_instance_id = #{workflowInstanceId}
        AND del_flag = 0
        ORDER BY workflow_task_id, sequence ASC, create_time ASC
    </select>

</mapper>
