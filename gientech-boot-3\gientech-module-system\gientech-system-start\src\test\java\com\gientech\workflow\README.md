# 工作流会签功能测试文档

## 概述

本测试套件为工作流会签功能提供了完整的单元测试和集成测试，确保会签功能的正确性、稳定性和性能。

## 测试结构

### 测试类组织

```
src/test/java/com/gientech/workflow/
├── CountersignWorkflowTest.java              # 主要集成测试类
├── service/
│   └── WorkflowCountersignTaskServiceTest.java  # Service层单元测试
├── util/
│   └── CountersignTestUtils.java             # 测试工具类
└── CountersignTestSuite.java                 # 测试套件
```

### 测试配置文件

```
src/test/resources/
├── application-test.yml                      # 测试环境配置
└── sql/
    └── test-data.sql                         # 测试数据初始化脚本
```

## 测试内容覆盖

### 1. Service层单元测试 (WorkflowCountersignTaskServiceTest)

- **会签任务创建测试**: 验证会签节点能正确创建主任务和多个会签子任务
- **会签任务完成测试**: 测试单个会签子任务的完成流程（approved/rejected状态）
- **会签完成策略测试**: 测试不同完成策略的逻辑
  - 全部同意 (all)
  - 任意一个同意 (any)
  - 超过半数同意 (majority)
  - 按百分比同意 (percentage)
- **会签任务查询测试**: 测试根据委托人查询待处理会签任务
- **会签统计功能测试**: 验证会签任务状态统计的准确性
- **异常场景测试**: 测试重复完成任务等异常情况

### 2. 集成测试 (CountersignWorkflowTest)

- **完整工作流程测试**: 验证从工作流启动到完成的整个流程
- **会签任务查询功能**: 测试各种查询接口的正确性
- **并发完成测试**: 测试多个会签人同时完成任务的并发场景
- **大规模性能测试**: 测试50个会签人的性能表现
- **异常场景测试**: 测试无效配置、事务回滚等异常情况

## 运行测试

### 运行单个测试类

```bash
# 运行Service层测试
mvn test -Dtest=WorkflowCountersignTaskServiceTest

# 运行集成测试
mvn test -Dtest=CountersignWorkflowTest
```

### 运行完整测试套件

```bash
# 运行所有会签相关测试
mvn test -Dtest=CountersignTestSuite
```

### 运行特定测试方法

```bash
# 运行特定的测试方法
mvn test -Dtest=CountersignWorkflowTest#testCompleteCountersignWorkflowAllApproved
```

## 测试数据

### 测试用会签人列表

```java
List<String> assignees = Arrays.asList(
    "manager1@dept001", 
    "manager2@dept002", 
    "manager3@dept003"
);
```

### 测试用会签配置

```java
Map<String, Object> countersignConfig = new HashMap<>();
countersignConfig.put("completionType", "majority");  // 完成策略
countersignConfig.put("sequential", false);           // 是否顺序会签
countersignConfig.put("allowDelegate", true);         // 是否允许委托
```

## 性能基准

### 性能测试指标

- **50个会签任务创建**: < 2秒
- **会签任务完成和流转**: < 1秒
- **并发会签完成**: 支持3个并发操作
- **总体流程完成**: < 5秒

### 并发测试

测试支持多个会签人同时完成任务，验证：
- 数据一致性
- 事务隔离性
- 性能表现

## 异常场景测试

### 1. 数据验证

- 空会签人列表
- 无效的会签配置
- 重复完成任务

### 2. 事务处理

- 事务回滚验证
- 数据一致性检查
- 并发冲突处理

### 3. 错误处理

- 不存在的任务ID
- 无效的状态转换
- 系统异常恢复

## 测试环境配置

### 数据库配置

测试使用H2内存数据库，配置如下：

```yaml
spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL
    username: sa
    password: 
```

### 日志配置

```yaml
logging:
  level:
    com.gientech.workflow: DEBUG
    org.springframework.transaction: DEBUG
```

## 测试工具类

### CountersignTestUtils

提供以下工具方法：

- `createTestWorkflowDefine()`: 创建测试用工作流定义
- `createTestBusinessVariables()`: 创建测试用业务变量
- `assertInstanceStatus()`: 验证工作流实例状态
- `assertTaskStatus()`: 验证任务状态
- `assertCountersignTaskStatus()`: 验证会签任务状态
- `assertPerformance()`: 验证性能指标

## 注意事项

### 1. 测试隔离

- 每个测试方法使用 `@Transactional` 确保数据隔离
- 测试数据在测试完成后自动回滚

### 2. 测试顺序

- 测试方法之间相互独立，可以任意顺序执行
- 使用时间戳确保测试数据的唯一性

### 3. 资源清理

- 测试完成后自动清理临时数据
- 使用 `@After` 方法进行必要的清理工作

## 故障排除

### 常见问题

1. **测试数据库连接失败**
   - 检查H2数据库依赖是否正确
   - 确认测试配置文件路径正确

2. **事务回滚失败**
   - 检查 `@Transactional` 注解配置
   - 确认测试方法没有捕获异常

3. **并发测试失败**
   - 检查线程池配置
   - 确认超时时间设置合理

### 调试建议

1. 启用DEBUG日志查看详细执行过程
2. 使用断点调试关键业务逻辑
3. 检查测试数据的准备和清理过程

## 扩展测试

### 添加新的测试用例

1. 在相应的测试类中添加新的测试方法
2. 使用 `CountersignTestUtils` 提供的工具方法
3. 确保测试方法的独立性和可重复性

### 性能测试扩展

1. 增加更大规模的会签人数测试
2. 测试更复杂的会签策略组合
3. 添加内存和CPU使用率监控
