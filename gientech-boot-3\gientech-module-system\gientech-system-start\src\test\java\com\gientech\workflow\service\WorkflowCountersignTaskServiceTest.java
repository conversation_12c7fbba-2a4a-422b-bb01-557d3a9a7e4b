package com.gientech.workflow.service;

import com.gientech.GientechSystemApplication;
import com.gientech.workflow.entity.WorkflowCountersignTask;
import com.gientech.workflow.enums.CountersignTaskStatus;
import com.gientech.workflow.mapper.WorkflowCountersignTaskMapper.CountersignTaskStatusCount;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 工作流会签任务服务测试类
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = GientechSystemApplication.class)
@Transactional
public class WorkflowCountersignTaskServiceTest {

    @Autowired
    private IWorkflowCountersignTaskService countersignTaskService;

    private String testWorkflowTaskId;
    private String testWorkflowInstanceId;
    private List<String> testAssignees;
    private Map<String, Object> testVariables;

    @Before
    public void setUp() {
        // 准备测试数据
        testWorkflowTaskId = "task_" + System.currentTimeMillis();
        testWorkflowInstanceId = "instance_" + System.currentTimeMillis();
        testAssignees = Arrays.asList("user1@dept001", "user2@dept002", "user3@dept003");

        testVariables = new HashMap<>();
        testVariables.put("businessId", "test_business_001");
        testVariables.put("applicant", "张三");
        testVariables.put("amount", 100000);

        log.info("测试数据准备完成 - 主任务ID: {}, 实例ID: {}", testWorkflowTaskId, testWorkflowInstanceId);
    }

    @After
    public void tearDown() {
        log.info("测试完成，清理测试数据");
    }

    /**
     * 测试创建会签任务
     */
    @Test
    public void testCreateCountersignTasks() {
        log.info("开始测试创建会签任务");

        // 执行创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 验证创建结果
        assertNotNull("创建的会签任务列表不应为空", createdTasks);
        assertEquals("会签任务数量应等于会签人数量", testAssignees.size(), createdTasks.size());

        // 验证每个会签任务的属性
        for (int i = 0; i < createdTasks.size(); i++) {
            WorkflowCountersignTask task = createdTasks.get(i);

            assertNotNull("会签任务ID不应为空", task.getId());
            assertEquals("主任务ID应正确", testWorkflowTaskId, task.getWorkflowTaskId());
            assertEquals("工作流实例ID应正确", testWorkflowInstanceId, task.getWorkflowInstanceId());
            assertEquals("任务状态应为待处理", CountersignTaskStatus.pending, task.getStatus());
            assertEquals("任务顺序应正确", Integer.valueOf(i + 1), task.getSequence());

            // 验证第一个任务为活跃状态，其他为非活跃状态
            if (i == 0) {
                assertTrue("第一个会签任务应为活跃状态", task.getActive());
            } else {
                assertFalse("非第一个会签任务应为非活跃状态", task.getActive());
            }

            // 验证会签人信息
            String expectedAssignee = testAssignees.get(i).split("@")[0];
            String expectedOrgCode = testAssignees.get(i).split("@")[1];
            assertEquals("会签人应正确", expectedAssignee, task.getAssignee());
            assertEquals("会签人机构应正确", expectedOrgCode, task.getAssigneeOrgCode());
        }

        log.info("创建会签任务测试通过");
    }

    /**
     * 测试完成会签任务 - 同意
     */
    @Test
    public void testCompleteCountersignTaskApproved() {
        log.info("开始测试完成会签任务 - 同意");

        // 先创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 完成第一个会签任务
        WorkflowCountersignTask firstTask = createdTasks.get(0);
        Map<String, Object> approvalVariables = new HashMap<>();
        approvalVariables.put("approvalComment", "同意申请");

        boolean result = countersignTaskService.completeCountersignTask(
                firstTask.getId(),
                CountersignTaskStatus.approved,
                "同意该申请",
                approvalVariables
        );

        // 验证完成结果
        assertTrue("完成会签任务应成功", result);

        // 验证任务状态更新
        WorkflowCountersignTask updatedTask = countersignTaskService.getById(firstTask.getId());
        assertNotNull("更新后的任务不应为空", updatedTask);
        assertEquals("任务状态应为已同意", CountersignTaskStatus.approved, updatedTask.getStatus());
        assertEquals("审批意见应正确", "同意该申请", updatedTask.getComment());
        assertNotNull("审批时间应不为空", updatedTask.getApprovalTime());
        assertFalse("任务应变为非活跃状态", updatedTask.getActive());

        // 验证下一个任务被激活
        if (createdTasks.size() > 1) {
            WorkflowCountersignTask nextTask = countersignTaskService.getById(createdTasks.get(1).getId());
            assertTrue("下一个任务应被激活", nextTask.getActive());
        }

        log.info("完成会签任务 - 同意测试通过");
    }

    /**
     * 测试完成会签任务 - 拒绝
     */
    @Test
    public void testCompleteCountersignTaskRejected() {
        log.info("开始测试完成会签任务 - 拒绝");

        // 先创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 完成第一个会签任务 - 拒绝
        WorkflowCountersignTask firstTask = createdTasks.get(0);
        boolean result = countersignTaskService.completeCountersignTask(
                firstTask.getId(),
                CountersignTaskStatus.rejected,
                "不同意该申请，理由：金额过大",
                new HashMap<>()
        );

        // 验证完成结果
        assertTrue("完成会签任务应成功", result);

        // 验证任务状态更新
        WorkflowCountersignTask updatedTask = countersignTaskService.getById(firstTask.getId());
        assertEquals("任务状态应为已拒绝", CountersignTaskStatus.rejected, updatedTask.getStatus());
        assertEquals("审批意见应正确", "不同意该申请，理由：金额过大", updatedTask.getComment());

        log.info("完成会签任务 - 拒绝测试通过");
    }

    /**
     * 测试查询待处理的会签任务
     */
    @Test
    public void testGetPendingCountersignTasksByAssignee() {
        log.info("开始测试查询待处理的会签任务");

        // 创建会签任务
        countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 查询第一个会签人的待处理任务
        String assignee = testAssignees.get(0).split("@")[0];
        String orgCode = testAssignees.get(0).split("@")[1];

        List<WorkflowCountersignTask> pendingTasks =
                countersignTaskService.getPendingCountersignTasksByAssignee(assignee, orgCode);

        // 验证查询结果
        assertNotNull("查询结果不应为空", pendingTasks);
        assertEquals("应该有一个待处理任务", 1, pendingTasks.size());

        WorkflowCountersignTask pendingTask = pendingTasks.get(0);
        assertEquals("会签人应正确", assignee, pendingTask.getAssignee());
        assertEquals("机构代码应正确", orgCode, pendingTask.getAssigneeOrgCode());
        assertEquals("任务状态应为待处理", CountersignTaskStatus.pending, pendingTask.getStatus());
        assertTrue("任务应为活跃状态", pendingTask.getActive());

        log.info("查询待处理的会签任务测试通过");
    }

    /**
     * 测试会签任务统计功能
     */
    @Test
    public void testGetCountersignTaskStatistics() {
        log.info("开始测试会签任务统计功能");

        // 创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 完成部分会签任务
        countersignTaskService.completeCountersignTask(
                createdTasks.get(0).getId(),
                CountersignTaskStatus.approved,
                "同意",
                new HashMap<>()
        );

        countersignTaskService.completeCountersignTask(
                createdTasks.get(1).getId(),
                CountersignTaskStatus.rejected,
                "拒绝",
                new HashMap<>()
        );

        // 获取统计信息
        List<CountersignTaskStatusCount> statistics =
                countersignTaskService.getCountersignTaskStatistics(testWorkflowTaskId);

        // 验证统计结果
        assertNotNull("统计结果不应为空", statistics);

        Map<CountersignTaskStatus, Integer> statusCountMap = new HashMap<>();
        for (CountersignTaskStatusCount stat : statistics) {
            statusCountMap.put(stat.getStatus(), stat.getCount());
        }

        assertEquals("应有1个已同意任务", Integer.valueOf(1), statusCountMap.get(CountersignTaskStatus.approved));
        assertEquals("应有1个已拒绝任务", Integer.valueOf(1), statusCountMap.get(CountersignTaskStatus.rejected));
        assertEquals("应有1个待处理任务", Integer.valueOf(1), statusCountMap.get(CountersignTaskStatus.pending));

        log.info("会签任务统计功能测试通过");
    }

    /**
     * 测试检查所有会签任务是否完成
     */
    @Test
    public void testIsAllCountersignTasksCompleted() {
        log.info("开始测试检查所有会签任务是否完成");

        // 创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 初始状态应该未完成
        assertFalse("初始状态应该未完成",
                countersignTaskService.isAllCountersignTasksCompleted(testWorkflowTaskId));

        // 完成所有会签任务
        for (WorkflowCountersignTask task : createdTasks) {
            countersignTaskService.completeCountersignTask(
                    task.getId(),
                    CountersignTaskStatus.approved,
                    "同意",
                    new HashMap<>()
            );
        }

        // 现在应该全部完成
        assertTrue("所有任务完成后应该返回true",
                countersignTaskService.isAllCountersignTasksCompleted(testWorkflowTaskId));

        log.info("检查所有会签任务是否完成测试通过");
    }

    /**
     * 测试会签完成策略 - 全部同意
     */
    @Test
    public void testCanCompleteMainTaskWithAllStrategy() {
        log.info("开始测试会签完成策略 - 全部同意");

        // 创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 配置全部同意策略
        Map<String, Object> config = new HashMap<>();
        config.put("completionType", "all");

        // 初始状态不能完成
        assertFalse("初始状态不能完成主任务",
                countersignTaskService.canCompleteMainTask(testWorkflowTaskId, config));

        // 完成前两个任务为同意
        for (int i = 0; i < 2; i++) {
            countersignTaskService.completeCountersignTask(
                    createdTasks.get(i).getId(),
                    CountersignTaskStatus.approved,
                    "同意",
                    new HashMap<>()
            );
        }

        // 还有一个未完成，不能完成主任务
        assertFalse("还有任务未完成，不能完成主任务",
                countersignTaskService.canCompleteMainTask(testWorkflowTaskId, config));

        // 完成最后一个任务
        countersignTaskService.completeCountersignTask(
                createdTasks.get(2).getId(),
                CountersignTaskStatus.approved,
                "同意",
                new HashMap<>()
        );

        // 现在可以完成主任务
        assertTrue("所有任务都同意，可以完成主任务",
                countersignTaskService.canCompleteMainTask(testWorkflowTaskId, config));

        log.info("会签完成策略 - 全部同意测试通过");
    }

    /**
     * 测试会签完成策略 - 任意一个同意
     */
    @Test
    public void testCanCompleteMainTaskWithAnyStrategy() {
        log.info("开始测试会签完成策略 - 任意一个同意");

        // 创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 配置任意一个同意策略
        Map<String, Object> config = new HashMap<>();
        config.put("completionType", "any");

        // 初始状态不能完成
        assertFalse("初始状态不能完成主任务",
                countersignTaskService.canCompleteMainTask(testWorkflowTaskId, config));

        // 完成第一个任务为同意
        countersignTaskService.completeCountersignTask(
                createdTasks.get(0).getId(),
                CountersignTaskStatus.approved,
                "同意",
                new HashMap<>()
        );

        // 现在可以完成主任务
        assertTrue("有一个任务同意，可以完成主任务",
                countersignTaskService.canCompleteMainTask(testWorkflowTaskId, config));

        log.info("会签完成策略 - 任意一个同意测试通过");
    }

    /**
     * 测试会签完成策略 - 超过半数同意
     */
    @Test
    public void testCanCompleteMainTaskWithMajorityStrategy() {
        log.info("开始测试会签完成策略 - 超过半数同意");

        // 创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 配置超过半数同意策略
        Map<String, Object> config = new HashMap<>();
        config.put("completionType", "majority");

        // 完成一个任务为同意（1/3，不超过半数）
        countersignTaskService.completeCountersignTask(
                createdTasks.get(0).getId(),
                CountersignTaskStatus.approved,
                "同意",
                new HashMap<>()
        );

        assertFalse("1个同意不超过半数，不能完成主任务",
                countersignTaskService.canCompleteMainTask(testWorkflowTaskId, config));

        // 完成第二个任务为同意（2/3，超过半数）
        countersignTaskService.completeCountersignTask(
                createdTasks.get(1).getId(),
                CountersignTaskStatus.approved,
                "同意",
                new HashMap<>()
        );

        assertTrue("2个同意超过半数，可以完成主任务",
                countersignTaskService.canCompleteMainTask(testWorkflowTaskId, config));

        log.info("会签完成策略 - 超过半数同意测试通过");
    }

    /**
     * 测试会签完成策略 - 按百分比
     */
    @Test
    public void testCanCompleteMainTaskWithPercentageStrategy() {
        log.info("开始测试会签完成策略 - 按百分比");

        // 创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 配置70%同意策略
        Map<String, Object> config = new HashMap<>();
        config.put("completionType", "percentage");
        config.put("requiredPercentage", 70.0);

        // 完成两个任务为同意（2/3 = 66.7%，不足70%）
        for (int i = 0; i < 2; i++) {
            countersignTaskService.completeCountersignTask(
                    createdTasks.get(i).getId(),
                    CountersignTaskStatus.approved,
                    "同意",
                    new HashMap<>()
            );
        }

        assertFalse("66.7%不足70%，不能完成主任务",
                countersignTaskService.canCompleteMainTask(testWorkflowTaskId, config));

        // 完成第三个任务为同意（3/3 = 100%，超过70%）
        countersignTaskService.completeCountersignTask(
                createdTasks.get(2).getId(),
                CountersignTaskStatus.approved,
                "同意",
                new HashMap<>()
        );

        assertTrue("100%超过70%，可以完成主任务",
                countersignTaskService.canCompleteMainTask(testWorkflowTaskId, config));

        log.info("会签完成策略 - 按百分比测试通过");
    }

    /**
     * 测试取消待处理的会签任务
     */
    @Test
    public void testCancelPendingCountersignTasks() {
        log.info("开始测试取消待处理的会签任务");

        // 创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        // 完成第一个任务
        countersignTaskService.completeCountersignTask(
                createdTasks.get(0).getId(),
                CountersignTaskStatus.approved,
                "同意",
                new HashMap<>()
        );

        // 取消剩余的待处理任务
        int cancelledCount = countersignTaskService.cancelPendingCountersignTasks(
                testWorkflowTaskId,
                "流程终止，取消剩余会签任务"
        );

        // 验证取消结果
        assertEquals("应该取消2个待处理任务", 2, cancelledCount);

        // 验证任务状态
        List<WorkflowCountersignTask> allTasks =
                countersignTaskService.getCountersignTasksByWorkflowTaskId(testWorkflowTaskId);

        int cancelledTaskCount = 0;
        for (WorkflowCountersignTask task : allTasks) {
            if (task.getStatus() == CountersignTaskStatus.cancelled) {
                cancelledTaskCount++;
                assertEquals("取消原因应正确", "流程终止，取消剩余会签任务", task.getComment());
                assertFalse("取消的任务应为非活跃状态", task.getActive());
            }
        }

        assertEquals("应该有2个任务被取消", 2, cancelledTaskCount);

        log.info("取消待处理的会签任务测试通过");
    }

    /**
     * 测试异常场景 - 重复完成任务
     */
    @Test
    public void testCompleteAlreadyCompletedTask() {
        log.info("开始测试异常场景 - 重复完成任务");

        // 创建会签任务
        List<WorkflowCountersignTask> createdTasks = countersignTaskService.createCountersignTasks(
                testWorkflowTaskId,
                testWorkflowInstanceId,
                testAssignees,
                "测试会签任务",
                testVariables
        );

        WorkflowCountersignTask firstTask = createdTasks.get(0);

        // 第一次完成任务
        boolean firstResult = countersignTaskService.completeCountersignTask(
                firstTask.getId(),
                CountersignTaskStatus.approved,
                "第一次完成",
                new HashMap<>()
        );
        assertTrue("第一次完成应该成功", firstResult);

        // 第二次完成同一个任务
        boolean secondResult = countersignTaskService.completeCountersignTask(
                firstTask.getId(),
                CountersignTaskStatus.rejected,
                "第二次完成",
                new HashMap<>()
        );
        assertFalse("第二次完成应该失败", secondResult);

        // 验证任务状态没有改变
        WorkflowCountersignTask updatedTask = countersignTaskService.getById(firstTask.getId());
        assertEquals("任务状态应该保持第一次完成的状态", CountersignTaskStatus.approved, updatedTask.getStatus());
        assertEquals("审批意见应该保持第一次的内容", "第一次完成", updatedTask.getComment());

        log.info("异常场景 - 重复完成任务测试通过");
    }
}
