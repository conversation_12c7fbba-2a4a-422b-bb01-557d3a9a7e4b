{"nodeList": [{"id": 1, "name": "开始", "type": "start", "assignee": "admin"}, {"id": 2, "name": "会签审批", "type": "countersign", "assignee": "user1,user2,user3", "assigneeOrgCode": "#businessObject.getDepartment()", "handler": "#businessObject.setStatus('reviewing')"}, {"id": 3, "name": "审批通过", "type": "end", "assignee": "admin", "handler": "#businessObject.setStatus('approved')"}, {"id": 4, "name": "审批拒绝", "type": "end", "assignee": "admin", "handler": "#businessObject.setStatus('rejected')"}], "edgeList": [{"id": "edge1", "sourceId": "1", "targetId": "2"}, {"id": "edge2", "sourceId": "2", "targetId": "3", "condition": "#countersignResult == 'approved'"}, {"id": "edge3", "sourceId": "2", "targetId": "4", "condition": "#countersignResult == 'rejected'"}]}