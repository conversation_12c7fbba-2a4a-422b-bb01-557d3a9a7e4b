package com.gientech.workflow.util;

import com.gientech.workflow.define.WorkflowDefine;
import com.gientech.workflow.entity.WorkflowCountersignTask;
import com.gientech.workflow.entity.WorkflowInstance;
import com.gientech.workflow.entity.WorkflowTask;
import com.gientech.workflow.enums.CountersignTaskStatus;
import com.gientech.workflow.enums.InstanceStatus;
import com.gientech.workflow.enums.TaskStatus;

import java.util.*;

/**
 * 会签测试工具类
 * 提供测试用的数据构造和验证方法
 *
 * <AUTHOR>
 * @date 2025年08月11日
 */
public class CountersignTestUtils {

    /**
     * 创建测试用的工作流定义
     */
    public static WorkflowDefine createTestWorkflowDefine(String businessKey) {
        String definition = """
                {
                   "nodeList": [
                     {
                       "id": "1",
                       "name": "开始",
                       "type": "start",
                       "assignee": "admin"
                     },
                     {
                       "id": "2",
                       "name": "会签审批",
                       "type": "countersign",
                       "assignee": "#countersignAssignees",
                       "assigneeOrgCode": "#businessObject.getDepartment()",
                       "handler": "#businessObject.setStatus('reviewing')"
                     },
                     {
                       "id": "3",
                       "name": "审批通过",
                       "type": "end",
                       "assignee": "admin",
                       "handler": "#businessObject.setStatus('approved')"
                     },
                     {
                       "id": "4",
                       "name": "审批拒绝",
                       "type": "end",
                       "assignee": "admin",
                       "handler": "#businessObject.setStatus('rejected')"
                     }
                   ],
                   "edgeList": [
                     {
                       "id": "edge1",
                       "sourceId": "1",
                       "targetId": "2"
                     },
                     {
                       "id": "edge2",
                       "sourceId": "2",
                       "targetId": "3",
                       "condition": "#countersignResult == 'approved'"
                     },
                     {
                       "id": "edge3",
                       "sourceId": "2",
                       "targetId": "4",
                       "condition": "#countersignResult == 'rejected'"
                     }
                   ]
                 }
                """;

        WorkflowDefine workflowDefine = new WorkflowDefine();
        workflowDefine.setBusinessKey(businessKey);
        workflowDefine.setName("会签测试流程");
        workflowDefine.setDefinition(definition);
        workflowDefine.setVersion("1.0.0");
        workflowDefine.setIsEffective("1");
        
        return workflowDefine;
    }

    /**
     * 创建测试用的业务变量
     */
    public static Map<String, Object> createTestBusinessVariables() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("businessId", "test_business_" + System.currentTimeMillis());
        variables.put("applicant", "张三");
        variables.put("amount", 100000);
        variables.put("department", "财务部");
        
        // 会签人列表
        List<String> assignees = Arrays.asList("manager1@dept001", "manager2@dept002", "manager3@dept003");
        variables.put("countersignAssignees", assignees);
        
        // 会签配置
        Map<String, Object> countersignConfig = new HashMap<>();
        countersignConfig.put("completionType", "majority");
        countersignConfig.put("sequential", false);
        countersignConfig.put("allowDelegate", true);
        variables.put("countersignConfig", countersignConfig);
        
        return variables;
    }

    /**
     * 创建测试用的会签配置
     */
    public static Map<String, Object> createCountersignConfig(String completionType) {
        Map<String, Object> config = new HashMap<>();
        config.put("completionType", completionType);
        config.put("sequential", false);
        config.put("allowDelegate", true);
        config.put("timeoutAction", "escalate");
        
        if ("percentage".equals(completionType)) {
            config.put("requiredPercentage", 70.0);
        }
        
        return config;
    }

    /**
     * 验证工作流实例状态
     */
    public static void assertInstanceStatus(WorkflowInstance instance, InstanceStatus expectedStatus) {
        if (instance == null) {
            throw new AssertionError("工作流实例不应为空");
        }
        if (!expectedStatus.equals(instance.getStatus())) {
            throw new AssertionError(String.format("工作流实例状态不匹配，期望: %s, 实际: %s", 
                    expectedStatus, instance.getStatus()));
        }
    }

    /**
     * 验证工作流任务状态
     */
    public static void assertTaskStatus(WorkflowTask task, TaskStatus expectedStatus) {
        if (task == null) {
            throw new AssertionError("工作流任务不应为空");
        }
        if (!expectedStatus.equals(task.getStatus())) {
            throw new AssertionError(String.format("工作流任务状态不匹配，期望: %s, 实际: %s", 
                    expectedStatus, task.getStatus()));
        }
    }

    /**
     * 验证会签任务状态
     */
    public static void assertCountersignTaskStatus(WorkflowCountersignTask task, CountersignTaskStatus expectedStatus) {
        if (task == null) {
            throw new AssertionError("会签任务不应为空");
        }
        if (!expectedStatus.equals(task.getStatus())) {
            throw new AssertionError(String.format("会签任务状态不匹配，期望: %s, 实际: %s", 
                    expectedStatus, task.getStatus()));
        }
    }

    /**
     * 验证会签任务列表的状态分布
     */
    public static void assertCountersignTaskStatusDistribution(List<WorkflowCountersignTask> tasks, 
                                                               Map<CountersignTaskStatus, Integer> expectedDistribution) {
        Map<CountersignTaskStatus, Integer> actualDistribution = new HashMap<>();
        
        for (WorkflowCountersignTask task : tasks) {
            actualDistribution.merge(task.getStatus(), 1, Integer::sum);
        }
        
        for (Map.Entry<CountersignTaskStatus, Integer> entry : expectedDistribution.entrySet()) {
            CountersignTaskStatus status = entry.getKey();
            Integer expectedCount = entry.getValue();
            Integer actualCount = actualDistribution.getOrDefault(status, 0);
            
            if (!expectedCount.equals(actualCount)) {
                throw new AssertionError(String.format("状态 %s 的任务数量不匹配，期望: %d, 实际: %d", 
                        status, expectedCount, actualCount));
            }
        }
    }

    /**
     * 创建大量会签人列表（用于性能测试）
     */
    public static List<String> createLargeAssigneeList(int count) {
        List<String> assignees = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            assignees.add("user" + i + "@dept" + String.format("%03d", i));
        }
        return assignees;
    }

    /**
     * 验证性能指标
     */
    public static void assertPerformance(long actualTime, long maxAllowedTime, String operation) {
        if (actualTime > maxAllowedTime) {
            throw new AssertionError(String.format("%s 操作耗时过长，期望小于: %dms, 实际: %dms", 
                    operation, maxAllowedTime, actualTime));
        }
    }

    /**
     * 生成唯一的测试ID
     */
    public static String generateTestId(String prefix) {
        return prefix + "_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 验证列表不为空且大小符合预期
     */
    public static void assertListSize(List<?> list, int expectedSize, String description) {
        if (list == null) {
            throw new AssertionError(description + " 不应为空");
        }
        if (list.size() != expectedSize) {
            throw new AssertionError(String.format("%s 大小不匹配，期望: %d, 实际: %d", 
                    description, expectedSize, list.size()));
        }
    }

    /**
     * 验证字符串不为空
     */
    public static void assertNotEmpty(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            throw new AssertionError(fieldName + " 不应为空");
        }
    }

    /**
     * 验证对象不为空
     */
    public static void assertNotNull(Object value, String description) {
        if (value == null) {
            throw new AssertionError(description + " 不应为空");
        }
    }
}
